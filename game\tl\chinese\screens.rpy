# Generated translation file

translate chinese strings:

    # game/screens.rpy:259
    old "Back"
    new "返回"

    # game/screens.rpy:260
    old "History"
    new "历史记录"

    # game/screens.rpy:261
    old "Skip"
    new "跳过"

    # game/screens.rpy:262
    old "Auto"
    new "自动"

    # game/screens.rpy:263
    old "Save"
    new "保存"

    # game/screens.rpy:264
    old "Q.Save"
    new "快速保存"

    # game/screens.rpy:265
    old "Q.Load"
    new "快速加载"

    # game/screens.rpy:266
    old "Prefs"
    new "设置"

    # game/screens.rpy:307
    old "Start"
    new "开始游戏"

    # game/screens.rpy:315
    old "Load"
    new "加载"

    # game/screens.rpy:317
    old "Preferences"
    new "设置"

    # game/screens.rpy:321
    old "End Replay"
    new "结束重播"

    # game/screens.rpy:325
    old "Main Menu"
    new "主菜单"

    # game/screens.rpy:327
    old "About"
    new "关于"

    # game/screens.rpy:332
    old "Help"
    new "帮助"

    # game/screens.rpy:338
    old "Quit"
    new "退出"

    # game/screens.rpy:478
    old "Return"
    new "返回"

    # game/screens.rpy:562
    old "Version [config.version!t]\n"
    new "Version[config.version!t]\n"

    # game/screens.rpy:568
    old "Made with {a=https://www.renpy.org/}Ren'Py{/a} [renpy.version_only].\n\n[renpy.license!t]"
    new "使用{a=https://www.renpy.org/}ren'py{/a}[renpy.version_only]创建.\n\n[renpy.license!t]"

    # game/screens.rpy:608
    old "Page {}"
    new "页{}"

    # game/screens.rpy:608
    old "Automatic saves"
    new "自动保存"

    # game/screens.rpy:608
    old "Quick saves"
    new "快速保存"

    # game/screens.rpy:650
    old "{#file_time}%A, %B %d %Y, %H:%M"
    new "{#file_time}a,bdy,h:m"

    # game/screens.rpy:650
    old "empty slot"
    new "空槽"

    # game/screens.rpy:667
    old "<"
    new "<"

    # game/screens.rpy:670
    old "{#auto_page}A"
    new "{#auto_page}A"

    # game/screens.rpy:673
    old "{#quick_page}Q"
    new "{#quick_page}Q"

    # game/screens.rpy:679
    old ">"
    new ">"

    # game/screens.rpy:736
    old "Display"
    new "显示"

    # game/screens.rpy:737
    old "Window"
    new "窗口"

    # game/screens.rpy:738
    old "Fullscreen"
    new "全屏"

    # game/screens.rpy:742
    old "Rollback Side"
    new "回退方向"

    # game/screens.rpy:743
    old "Disable"
    new "禁用"

    # game/screens.rpy:744
    old "Left"
    new "左"

    # game/screens.rpy:745
    old "Right"
    new "右"

    # game/screens.rpy:750
    old "Unseen Text"
    new "未读文本"

    # game/screens.rpy:751
    old "After Choices"
    new "选择后跳过"

    # game/screens.rpy:752
    old "Transitions"
    new "转场效果"

    # game/screens.rpy:765
    old "Text Speed"
    new "文本速度"

    # game/screens.rpy:769
    old "Auto-Forward Time"
    new "自动播放时间"

    # game/screens.rpy:776
    old "Music Volume"
    new "音乐音量"

    # game/screens.rpy:783
    old "Sound Volume"
    new "音效音量"

    # game/screens.rpy:789
    old "Test"
    new "测试"

    # game/screens.rpy:793
    old "Voice Volume"
    new "语音音量"

    # game/screens.rpy:804
    old "Mute All"
    new "全部静音"

    # game/screens.rpy:923
    old "The dialogue history is empty."
    new "对话历史记录为空。"

    # game/screens.rpy:993
    old "Keyboard"
    new "键盘"

    # game/screens.rpy:994
    old "Mouse"
    new "鼠标"

    # game/screens.rpy:997
    old "Gamepad"
    new "手柄"

    # game/screens.rpy:1010
    old "Enter"
    new "回车键"

    # game/screens.rpy:1011
    old "Advances dialogue and activates the interface."
    new "推进对话并激活界面。"

    # game/screens.rpy:1014
    old "Space"
    new "空格键"

    # game/screens.rpy:1015
    old "Advances dialogue without selecting choices."
    new "推进对话但不选择选项。"

    # game/screens.rpy:1018
    old "Arrow Keys"
    new "方向键"

    # game/screens.rpy:1019
    old "Navigate the interface."
    new "导航界面。"

    # game/screens.rpy:1022
    old "Escape"
    new "ESC键"

    # game/screens.rpy:1023
    old "Accesses the game menu."
    new "打开游戏菜单。"

    # game/screens.rpy:1026
    old "Ctrl"
    new "Ctrl键"

    # game/screens.rpy:1027
    old "Skips dialogue while held down."
    new "按住时跳过对话。"

    # game/screens.rpy:1030
    old "Tab"
    new "Tab键"

    # game/screens.rpy:1031
    old "Toggles dialogue skipping."
    new "切换对话跳过模式。"

    # game/screens.rpy:1034
    old "Page Up"
    new "Page Up键"

    # game/screens.rpy:1035
    old "Rolls back to earlier dialogue."
    new "回退到之前的对话。"

    # game/screens.rpy:1038
    old "Page Down"
    new "Page Down键"

    # game/screens.rpy:1039
    old "Rolls forward to later dialogue."
    new "前进到之后的对话。"

    # game/screens.rpy:1043
    old "Hides the user interface."
    new "隐藏用户界面。"

    # game/screens.rpy:1047
    old "Takes a screenshot."
    new "截取屏幕截图。"

    # game/screens.rpy:1051
    old "Toggles assistive {a=https://www.renpy.org/l/voicing}self-voicing{/a}."
    new "切换辅助{a=https://www.renpy.org/l/voicing}语音朗读{/a}。"

    # game/screens.rpy:1057
    old "Left Click"
    new "鼠标左键"

    # game/screens.rpy:1061
    old "Middle Click"
    new "鼠标中键"

    # game/screens.rpy:1065
    old "Right Click"
    new "鼠标右键"

    # game/screens.rpy:1069
    old "Mouse Wheel Up\nClick Rollback Side"
    new "鼠标滚轮向上\n点击回退区域"

    # game/screens.rpy:1073
    old "Mouse Wheel Down"
    new "鼠标滚轮向下"

    # game/screens.rpy:1080
    old "Right Trigger\nA/Bottom Button"
    new "右扳机键\nA键/下方按钮"

    # game/screens.rpy:1084
    old "Left Trigger\nLeft Shoulder"
    new "左扳机键\n左肩键"

    # game/screens.rpy:1088
    old "Right Shoulder"
    new "右肩键"

    # game/screens.rpy:1093
    old "D-Pad, Sticks"
    new "方向键、摇杆"

    # game/screens.rpy:1097
    old "Start, Guide"
    new "开始键、指南键"

    # game/screens.rpy:1101
    old "Y/Top Button"
    new "Y键/上方按钮"

    # game/screens.rpy:1104
    old "Calibrate"
    new "校准"

    # game/screens.rpy:1169
    old "Yes"
    new "是"

    # game/screens.rpy:1170
    old "No"
    new "否"

    # game/screens.rpy:1216
    old "Skipping"
    new "正在跳过"

    # game/screens.rpy:1439
    old "Menu"
    new "菜单"
